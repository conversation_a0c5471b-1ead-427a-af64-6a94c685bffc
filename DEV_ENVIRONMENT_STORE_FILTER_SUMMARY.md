# 开发环境门店过滤功能实现总结

## 需求背景

在开发环境中，目前只有1001和4008两个门店有数据，但是同步任务会尝试同步所有门店，这导致：
1. 浪费时间同步没有数据的门店
2. 产生大量无效的API调用
3. 影响开发环境的同步效率

## 解决方案

添加了开发环境专用的门店过滤功能，只在开发环境下同步指定的门店，生产环境不受影响。

## 实现详情

### 1. 配置文件修改

在 `src/main/resources/application-dev.yml` 中添加了开发环境专用配置：

```yaml
pos:
  sync:
    # 开发环境专用配置：只同步有数据的门店
    dev-only-stores: 1001,4008  # 开发环境只同步这些门店，生产环境忽略此配置
    
    operation-log:
      # 开发环境专用配置：只同步有数据的门店
      dev-only-stores: 1001,4008  # 开发环境只同步这些门店，生产环境忽略此配置
```

### 2. PosOrderSyncScheduler.java 修改

#### 新增配置属性
```java
@Value("${pos.sync.dev-only-stores:}")
private String devOnlyStores;

@Value("${spring.profiles.active:}")
private String activeProfile;
```

#### 添加门店过滤逻辑
在所有获取门店列表的地方添加了过滤：
```java
// 分页获取门店数据
storePage = posStoreService.getAllStores(PageRequest.of(pageNumber, batchStoreSize));
List<PosStore> stores = storePage.getContent();

// 开发环境过滤门店
stores = filterStoresForDev(stores);
```

#### 过滤方法实现
```java
/**
 * 开发环境门店过滤
 * 如果配置了 dev-only-stores 且当前是开发环境，则只同步指定的门店
 */
private List<PosStore> filterStoresForDev(List<PosStore> stores) {
    // 只在开发环境且配置了 dev-only-stores 时进行过滤
    if (!"dev".equals(activeProfile) || devOnlyStores == null || devOnlyStores.trim().isEmpty()) {
        return stores;
    }

    // 解析配置的门店ID列表
    List<String> allowedStoreIds = Arrays.stream(devOnlyStores.split(","))
            .map(String::trim)
            .filter(s -> !s.isEmpty())
            .collect(Collectors.toList());

    if (allowedStoreIds.isEmpty()) {
        return stores;
    }

    // 过滤门店
    List<PosStore> filteredStores = stores.stream()
            .filter(store -> allowedStoreIds.contains(store.getStoreId()))
            .collect(Collectors.toList());

    if (filteredStores.size() < stores.size()) {
        log.info("Dev environment: filtered {} stores to {} stores (allowed: {})", 
                stores.size(), filteredStores.size(), allowedStoreIds);
    }

    return filteredStores;
}
```

### 3. PosOperationLogSyncScheduler.java 修改

实现了与订单同步调度器相同的过滤逻辑：

#### 新增配置属性
```java
@Value("${pos.sync.operation-log.dev-only-stores:}")
private String devOnlyStores;

@Value("${spring.profiles.active:}")
private String activeProfile;
```

#### 添加门店过滤逻辑
在增量同步和全量同步的门店获取处都添加了过滤：
```java
// 开发环境过滤门店
stores = filterStoresForDev(stores);
```

#### 过滤方法实现
```java
/**
 * 开发环境门店过滤
 * 如果配置了 dev-only-stores 且当前是开发环境，则只同步指定的门店
 */
private List<PosStore> filterStoresForDev(List<PosStore> stores) {
    // 只在开发环境且配置了 dev-only-stores 时进行过滤
    if (!"dev".equals(activeProfile) || devOnlyStores == null || devOnlyStores.trim().isEmpty()) {
        return stores;
    }

    // 解析配置的门店ID列表
    List<String> allowedStoreIds = Arrays.stream(devOnlyStores.split(","))
            .map(String::trim)
            .filter(s -> !s.isEmpty())
            .collect(Collectors.toList());

    if (allowedStoreIds.isEmpty()) {
        return stores;
    }

    // 过滤门店
    List<PosStore> filteredStores = stores.stream()
            .filter(store -> allowedStoreIds.contains(store.getStoreId()))
            .collect(Collectors.toList());

    if (filteredStores.size() < stores.size()) {
        log.info("Dev environment: filtered {} operation log stores to {} stores (allowed: {})", 
                stores.size(), filteredStores.size(), allowedStoreIds);
    }

    return filteredStores;
}
```

## 功能特点

### 1. 环境隔离
- 只在开发环境（`spring.profiles.active=dev`）生效
- 生产环境完全不受影响，保持原有逻辑

### 2. 配置灵活
- 通过配置文件控制，无需修改代码
- 支持多个门店ID，用逗号分隔
- 如果不配置或配置为空，则不进行过滤

### 3. 日志记录
- 当进行过滤时会记录日志，便于调试
- 显示过滤前后的门店数量和允许的门店列表

### 4. 向后兼容
- 不影响现有功能
- 如果配置错误或为空，会回退到原有逻辑

## 使用方法

### 开发环境配置
在 `application-dev.yml` 中配置需要同步的门店：
```yaml
pos:
  sync:
    dev-only-stores: 1001,4008
    operation-log:
      dev-only-stores: 1001,4008
```

### 生产环境配置
生产环境的 `application-prod.yml` 中不需要添加这些配置，或者可以留空：
```yaml
pos:
  sync:
    dev-only-stores:  # 留空或不配置
    operation-log:
      dev-only-stores:  # 留空或不配置
```

## 预期效果

### 开发环境
- 订单同步只会处理1001和4008门店
- 收银日志同步只会处理1001和4008门店
- 大幅减少无效的API调用
- 提高同步效率

### 生产环境
- 保持原有逻辑，同步所有门店
- 不受任何影响

## 日志示例

当过滤生效时，会看到类似的日志：
```
INFO  - Dev environment: filtered 100 stores to 2 stores (allowed: [1001, 4008])
INFO  - Dev environment: filtered 100 operation log stores to 2 stores (allowed: [1001, 4008])
```

## 测试建议

1. **开发环境测试**：
   - 启动应用，观察同步日志
   - 确认只有1001和4008门店被同步
   - 检查过滤日志是否正确显示

2. **生产环境验证**：
   - 确保生产环境配置中没有 `dev-only-stores` 配置
   - 或者配置为空值
   - 验证所有门店都能正常同步

3. **配置测试**：
   - 测试不同的门店ID组合
   - 测试空配置的情况
   - 测试错误配置的容错性

## 扩展性

如果将来需要添加更多的开发环境专用门店，只需要修改配置文件：
```yaml
pos:
  sync:
    dev-only-stores: 1001,4008,1005,2001  # 添加更多门店
```

这个功能为开发环境提供了更好的同步效率，同时保持了生产环境的稳定性。
