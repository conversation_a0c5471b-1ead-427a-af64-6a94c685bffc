package com.rtpos.server.controller;

import com.rtpos.server.dto.StandardResponse;
import com.rtpos.server.dto.DashboardRequest;
import com.rtpos.server.dto.DashboardResponse;
import com.rtpos.server.dto.EnergyOptimizationRequest;
import com.rtpos.server.dto.EnergyOptimizationResponse;
import com.rtpos.server.dto.OrderAnalysisRequest;
import com.rtpos.server.dto.OrderAnalysisResponse;
import com.rtpos.server.dto.PosUsageRequest;
import com.rtpos.server.dto.PosUsageResponse;
import com.rtpos.server.dto.DeviceStatusRequest;
import com.rtpos.server.dto.DeviceStatusResponse;
import com.rtpos.server.dto.EnergyConsumptionRequest;
import com.rtpos.server.dto.EnergyConsumptionResponse;
import com.rtpos.server.dto.StoreListResponse;
import com.rtpos.server.service.DashboardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * Dashboard数据控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/pos/dashboard")
@RequiredArgsConstructor
@Validated
@Tag(name = "Dashboard管理", description = "POS看盘Dashboard相关API")
public class DashboardController {

    private final DashboardService dashboardService;

    @Operation(summary = "获取Dashboard整合数据", description = "获取指定门店的Dashboard整合数据，包括设备状态、订单分布、使用率等")
    @PostMapping("/data")
    public ResponseEntity<StandardResponse<DashboardResponse>> getDashboardData(
            @Parameter(description = "Dashboard数据查询请求")
            @Valid @RequestBody DashboardRequest request) {

        log.info("Getting dashboard data for store: {}, date: {}", request.getStoreId(), request.getDate());

        try {
            DashboardResponse data = dashboardService.getDashboardData(request);
            return ResponseEntity.ok(StandardResponse.success("获取Dashboard数据成功", data));

        } catch (Exception e) {
            log.error("Failed to get dashboard data for store: {}", request.getStoreId(), e);
            return ResponseEntity.ok(StandardResponse.error("获取Dashboard数据失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "获取能耗优化建议数据", description = "获取指定门店的能耗优化建议，包括优化措施、设备耗电排行等")
    @PostMapping("/energy-optimization")
    public ResponseEntity<StandardResponse<EnergyOptimizationResponse>> getEnergyOptimizationData(
            @Parameter(description = "能耗优化数据查询请求")
            @Valid @RequestBody EnergyOptimizationRequest request) {

        log.info("Getting energy optimization data for store: {}", request.getStoreId());

        try {
            EnergyOptimizationResponse data = dashboardService.getEnergyOptimizationData(request);
            return ResponseEntity.ok(StandardResponse.success("获取能耗优化数据成功", data));

        } catch (Exception e) {
            log.error("Failed to get energy optimization data for store: {}", request.getStoreId(), e);
            return ResponseEntity.ok(StandardResponse.error("获取能耗优化数据失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "获取订单分析数据", description = "获取指定门店的订单时段分析数据，包括订单分布、高峰期分析等")
    @PostMapping("/order-analysis")
    public ResponseEntity<StandardResponse<OrderAnalysisResponse>> getOrderAnalysisData(
            @Parameter(description = "订单分析数据查询请求")
            @Valid @RequestBody OrderAnalysisRequest request) {

        log.info("Getting order analysis data for store: {}, date: {}", request.getStoreId(), request.getDate());

        try {
            OrderAnalysisResponse data = dashboardService.getOrderAnalysisData(request);
            return ResponseEntity.ok(StandardResponse.success("获取订单分析数据成功", data));

        } catch (Exception e) {
            log.error("Failed to get order analysis data for store: {}", request.getStoreId(), e);
            return ResponseEntity.ok(StandardResponse.error("获取订单分析数据失败: " + e.getMessage()));
        }
    }

    @Operation(summary = "获取POS使用率分析数据", description = "获取指定门店的POS设备使用率分析数据，包括设备详情、使用率趋势等")
    @PostMapping("/pos-usage")
    public ResponseEntity<StandardResponse<PosUsageResponse>> getPosUsageData(
            @Parameter(description = "POS使用率数据查询请求")
            @Valid @RequestBody PosUsageRequest request) {

        log.info("Getting POS usage data for store: {}, date: {}", request.getStoreId(), request.getDate());

        try {
            PosUsageResponse data = dashboardService.getPosUsageData(request);
            return ResponseEntity.ok(StandardResponse.success("获取POS使用率数据成功", data));

        } catch (Exception e) {
            log.error("Failed to get POS usage data for store: {}", request.getStoreId(), e);
            return ResponseEntity.ok(StandardResponse.error("获取POS使用率数据失败: " + e.getMessage()));
        }
    }
}
