package com.rtpos.server.service;

import com.rtpos.server.dto.DashboardRequest;
import com.rtpos.server.dto.DashboardResponse;
import com.rtpos.server.dto.EnergyOptimizationRequest;
import com.rtpos.server.dto.EnergyOptimizationResponse;
import com.rtpos.server.dto.OrderAnalysisRequest;
import com.rtpos.server.dto.OrderAnalysisResponse;
import com.rtpos.server.dto.PosUsageRequest;
import com.rtpos.server.dto.PosUsageResponse;

/**
 * Dashboard数据服务接口
 *
 * <AUTHOR>
 */
public interface DashboardService {

    /**
     * 获取Dashboard整合数据
     *
     * @param request 查询请求
     * @return Dashboard数据响应
     */
    DashboardResponse getDashboardData(DashboardRequest request);

    /**
     * 获取能耗优化建议数据
     *
     * @param request 查询请求
     * @return 能耗优化数据响应
     */
    EnergyOptimizationResponse getEnergyOptimizationData(EnergyOptimizationRequest request);

    /**
     * 获取订单分析数据
     *
     * @param request 查询请求
     * @return 订单分析数据响应
     */
    OrderAnalysisResponse getOrderAnalysisData(OrderAnalysisRequest request);

    /**
     * 获取POS使用率分析数据
     *
     * @param request 查询请求
     * @return POS使用率数据响应
     */
    PosUsageResponse getPosUsageData(PosUsageRequest request);
}
