package com.rtpos.server.service.impl;

import com.rtpos.server.dto.*;
import com.rtpos.server.entity.PosOperationLog;
import com.rtpos.server.entity.PosOrder;
import com.rtpos.server.repository.PosOperationLogRepository;
import com.rtpos.server.repository.PosOrderRepository;
import com.rtpos.server.service.DashboardService;
import com.rtpos.server.service.PosStoreStatusService;
import com.rtpos.server.service.PosStoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Dashboard数据服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DashboardServiceImpl implements DashboardService {

    private final PosStoreStatusService posStoreStatusService;
    private final PosOrderRepository posOrderRepository;
    private final PosOperationLogRepository posOperationLogRepository;

    @Override
    public DashboardResponse getDashboardData(DashboardRequest request) {
        log.info("Getting dashboard data for store: {}, date: {}", request.getStoreId(), request.getDate());

        DashboardResponse response = new DashboardResponse();
        
        // 设置默认日期为当天
        LocalDate queryDate = request.getDate() != null ? request.getDate() : LocalDate.now();
        
        try {
            // 1. 获取门店设备状态信息
            PosStoreStatusRequest statusRequest = new PosStoreStatusRequest();
            statusRequest.setStoreNo(Integer.parseInt(request.getStoreId()));
            PosStoreStatusResponse storeStatus = posStoreStatusService.getStoreStatus(statusRequest);
            
            // 2. 填充设备状态数据
            fillDeviceStatusData(response, storeStatus);
            
            // 3. 获取订单数据并计算时段分布
            fillOrderTrendData(response, request.getStoreId(), queryDate);
            
            // 4. 计算设备使用率
            fillUsageRateData(response, request.getStoreId(), queryDate, storeStatus);
            
            // 5. 填充能耗数据（模拟计算）
            fillEnergyData(response);
            
            log.info("Successfully generated dashboard data for store: {}", request.getStoreId());
            
        } catch (Exception e) {
            log.error("Failed to get dashboard data for store: {}", request.getStoreId(), e);
            throw new RuntimeException("获取Dashboard数据失败: " + e.getMessage(), e);
        }
        
        return response;
    }

    /**
     * 填充设备状态数据
     */
    private void fillDeviceStatusData(DashboardResponse response, PosStoreStatusResponse storeStatus) {
        if (storeStatus != null && storeStatus.isSuccess() && storeStatus.getBody() != null) {
            var body = storeStatus.getBody();
            
            response.setTotalDevices(body.getStoreAllPosCount());
            response.setOnlineDevices(body.getAllOnlPosCount());
            response.setManualPosCount(body.getManualPosCount());
            response.setSelfServicePosCount(body.getSelfPosCount());
            
            // 设备状态分布
            DashboardResponse.DeviceStatusDistribution distribution = new DashboardResponse.DeviceStatusDistribution();
            
            // 人工POS状态
            DashboardResponse.DeviceStatus manualStatus = new DashboardResponse.DeviceStatus();
            manualStatus.setActive(body.getOnlManualPosNum());
            manualStatus.setIdle(0); // 暂时设为0，可以根据实际业务逻辑计算
            manualStatus.setOffline(body.getManualPosCount() - body.getOnlManualPosNum());
            distribution.setManual(manualStatus);
            
            // 自助POS状态
            DashboardResponse.DeviceStatus selfServiceStatus = new DashboardResponse.DeviceStatus();
            selfServiceStatus.setActive(body.getOnlSelfPosNum());
            selfServiceStatus.setIdle(0); // 暂时设为0，可以根据实际业务逻辑计算
            selfServiceStatus.setOffline(body.getSelfPosCount() - body.getOnlSelfPosNum());
            distribution.setSelfService(selfServiceStatus);
            
            response.setDeviceStatusDistribution(distribution);
        } else {
            // 设置默认值
            response.setTotalDevices(0);
            response.setOnlineDevices(0);
            response.setManualPosCount(0);
            response.setSelfServicePosCount(0);
            
            DashboardResponse.DeviceStatusDistribution distribution = new DashboardResponse.DeviceStatusDistribution();
            DashboardResponse.DeviceStatus defaultStatus = new DashboardResponse.DeviceStatus();
            defaultStatus.setActive(0);
            defaultStatus.setIdle(0);
            defaultStatus.setOffline(0);
            distribution.setManual(defaultStatus);
            distribution.setSelfService(defaultStatus);
            response.setDeviceStatusDistribution(distribution);
        }
    }

    /**
     * 填充订单时段分布数据
     */
    private void fillOrderTrendData(DashboardResponse response, String storeId, LocalDate queryDate) {
        try {
            LocalDateTime startTime = queryDate.atStartOfDay();
            LocalDateTime endTime = queryDate.atTime(LocalTime.MAX);

            log.debug("Querying orders for store: {}, date range: {} to {}", storeId, startTime, endTime);

            // 查询当天的订单数据
            List<PosOrder> orders = posOrderRepository.findByStoreIdAndOrderTimeDateTimeBetween(
                    storeId, startTime, endTime);

            log.debug("Found {} orders for store: {}", orders != null ? orders.size() : 0, storeId);

            // 按小时分组统计订单
            Map<Integer, Map<String, Integer>> hourlyStats = new HashMap<>();

            // 初始化24小时数据
            for (int hour = 0; hour < 24; hour++) {
                Map<String, Integer> stats = new HashMap<>();
                stats.put("manual", 0);
                stats.put("selfService", 0);
                hourlyStats.put(hour, stats);
            }

            // 统计订单数据
            if (orders != null) {
                for (PosOrder order : orders) {
                    if (order.getOrderTimeDateTime() != null && order.getOutOrderId() != null) {
                        int hour = order.getOrderTimeDateTime().getHour();
                        String deviceType = getDeviceTypeFromOrderId(order.getOutOrderId());

                        Map<String, Integer> stats = hourlyStats.get(hour);
                        if (stats != null) {
                            Integer currentCount = stats.get(deviceType);
                            if (currentCount != null) {
                                stats.put(deviceType, currentCount + 1);
                            } else {
                                log.warn("Device type '{}' not found in stats map for hour {}", deviceType, hour);
                                stats.put(deviceType, 1);
                            }
                        } else {
                            log.warn("Stats map not found for hour: {}", hour);
                        }
                    }
                }
            }

            // 转换为响应格式
            List<DashboardResponse.HourlyOrderTrendData> trendData = new ArrayList<>();
            for (int hour = 0; hour < 24; hour++) {
                DashboardResponse.HourlyOrderTrendData data = new DashboardResponse.HourlyOrderTrendData();
                data.setHour(String.format("%02d:00", hour));
                Map<String, Integer> stats = hourlyStats.get(hour);

                if (stats != null) {
                    Integer manualCount = stats.get("manual");
                    Integer selfServiceCount = stats.get("selfService");

                    data.setManual(manualCount != null ? manualCount : 0);
                    data.setSelfService(selfServiceCount != null ? selfServiceCount : 0);
                } else {
                    log.warn("Stats map is null for hour: {}, setting default values", hour);
                    data.setManual(0);
                    data.setSelfService(0);
                }

                trendData.add(data);
            }

            response.setHourlyOrderTrend(trendData);
            log.debug("Successfully generated hourly order trend data with {} entries", trendData.size());

        } catch (Exception e) {
            log.error("Error filling order trend data for store: {}, date: {}", storeId, queryDate, e);

            // 设置默认的空数据
            List<DashboardResponse.HourlyOrderTrendData> defaultTrendData = new ArrayList<>();
            for (int hour = 0; hour < 24; hour++) {
                DashboardResponse.HourlyOrderTrendData data = new DashboardResponse.HourlyOrderTrendData();
                data.setHour(String.format("%02d:00", hour));
                data.setManual(0);
                data.setSelfService(0);
                defaultTrendData.add(data);
            }
            response.setHourlyOrderTrend(defaultTrendData);
        }
    }

    /**
     * 根据订单ID判断设备类型
     * 第一个字符：9=人工POS，8=自助POS，7=移动POS
     */
    private String getDeviceTypeFromOrderId(String outOrderId) {
        if (outOrderId != null && !outOrderId.isEmpty()) {
            char firstChar = outOrderId.charAt(0);
            switch (firstChar) {
                case '9':
                    return "manual";
                case '8':
                    return "selfService";
                case '7':
                    // 移动POS归类为人工POS
                    return "manual";
                default:
                    log.debug("Unknown device type for order ID: {}, defaulting to manual", outOrderId);
                    return "manual"; // 默认为人工POS
            }
        }
        log.debug("Order ID is null or empty, defaulting to manual");
        return "manual";
    }

    /**
     * 填充使用率数据
     */
    private void fillUsageRateData(DashboardResponse response, String storeId, LocalDate queryDate, 
                                   PosStoreStatusResponse storeStatus) {
        
        // 计算平均使用率（基于操作日志）
        double averageUsageRate = calculateAverageUsageRate(storeId, queryDate, storeStatus);
        response.setAverageUsageRate(averageUsageRate);
        
        // 生成使用率趋势数据（最近7天）
        List<DashboardResponse.UsageRateTrendData> trendData = new ArrayList<>();
        for (int i = 6; i >= 0; i--) {
            LocalDate date = queryDate.minusDays(i);
            DashboardResponse.UsageRateTrendData data = new DashboardResponse.UsageRateTrendData();
            data.setDate(date.format(DateTimeFormatter.ofPattern("MM-dd")));
            
            // 这里可以根据实际业务逻辑计算每天的使用率
            // 暂时使用模拟数据
            data.setManual(40.0 + Math.random() * 20);
            data.setSelfService(35.0 + Math.random() * 15);
            
            trendData.add(data);
        }
        response.setUsageRateTrend(trendData);
    }

    /**
     * 计算平均使用率
     */
    private double calculateAverageUsageRate(String storeId, LocalDate queryDate, PosStoreStatusResponse storeStatus) {
        if (storeStatus == null || !storeStatus.isSuccess() || storeStatus.getBody() == null) {
            return 0.0;
        }
        
        var body = storeStatus.getBody();
        List<Integer> onlinePosNos = body.getOnline();
        
        if (onlinePosNos == null || onlinePosNos.isEmpty()) {
            return 0.0;
        }
        
        LocalDateTime startTime = queryDate.atStartOfDay();
        LocalDateTime endTime = queryDate.atTime(LocalTime.MAX);
        
        // 查询在线设备的操作日志
        List<PosOperationLog> operationLogs = posOperationLogRepository
                .findByStoreNoAndPosNoInAndOccurrenceTimeDateTimeBetween(
                        Integer.parseInt(storeId), onlinePosNos, startTime, endTime);
        
        // 计算使用率（简化计算：有操作日志的设备视为在使用）
        Set<Integer> activePosNos = operationLogs.stream()
                .map(PosOperationLog::getPosNo)
                .collect(Collectors.toSet());
        
        if (onlinePosNos.isEmpty()) {
            return 0.0;
        }
        
        return (double) activePosNos.size() / onlinePosNos.size() * 100;
    }

    /**
     * 填充能耗数据（模拟计算）
     */
    private void fillEnergyData(DashboardResponse response) {
        // 基于设备数量模拟计算能耗
        int totalDevices = response.getTotalDevices() != null ? response.getTotalDevices() : 0;
        int onlineDevices = response.getOnlineDevices() != null ? response.getOnlineDevices() : 0;
        
        // 模拟计算：每台在线设备每天耗电约1.5kWh
        double dailyConsumption = onlineDevices * 1.5;
        response.setDailyPowerConsumption(dailyConsumption);
        response.setMonthlyPowerConsumption(dailyConsumption * 30);
        response.setYearlyProjectedSavings(dailyConsumption * 365 * 0.1); // 假设节能10%
        
        // 生成节能趋势数据（最近7天）
        List<DashboardResponse.EnergySavingTrendData> energyTrend = new ArrayList<>();
        for (int i = 6; i >= 0; i--) {
            LocalDate date = LocalDate.now().minusDays(i);
            DashboardResponse.EnergySavingTrendData data = new DashboardResponse.EnergySavingTrendData();
            data.setDate(date.format(DateTimeFormatter.ofPattern("MM-dd")));
            data.setSaving(dailyConsumption * 0.1 + Math.random() * 5); // 模拟节能数据
            energyTrend.add(data);
        }
        response.setEnergySavingTrend(energyTrend);
    }

    @Override
    public EnergyOptimizationResponse getEnergyOptimizationData(EnergyOptimizationRequest request) {
        log.info("Getting energy optimization data for store: {}", request.getStoreId());

        EnergyOptimizationResponse response = new EnergyOptimizationResponse();

        // 设置默认日期为当天
        LocalDate queryDate = request.getDate() != null ? request.getDate() : LocalDate.now();

        try {
            // 1. 获取门店设备状态信息
            PosStoreStatusRequest statusRequest = new PosStoreStatusRequest();
            statusRequest.setStoreNo(Integer.parseInt(request.getStoreId()));
            PosStoreStatusResponse storeStatus = posStoreStatusService.getStoreStatus(statusRequest);

            // 2. 填充能耗基础数据
            fillEnergyOptimizationBasicData(response, storeStatus);

            // 3. 生成优化建议
            fillOptimizationSuggestions(response, request.getStoreId(), queryDate, storeStatus);

            // 4. 生成设备耗电量排行
            fillDeviceConsumptionRanking(response, request.getStoreId(), queryDate, storeStatus);

            log.info("Successfully generated energy optimization data for store: {}", request.getStoreId());

        } catch (Exception e) {
            log.error("Failed to get energy optimization data for store: {}", request.getStoreId(), e);
            throw new RuntimeException("获取能耗优化数据失败: " + e.getMessage(), e);
        }

        return response;
    }

    @Override
    public OrderAnalysisResponse getOrderAnalysisData(OrderAnalysisRequest request) {
        log.info("Getting order analysis data for store: {}, date: {}", request.getStoreId(), request.getDate());

        OrderAnalysisResponse response = new OrderAnalysisResponse();

        // 设置默认日期为当天
        LocalDate queryDate = request.getDate() != null ? request.getDate() : LocalDate.now();

        try {
            // 1. 填充今日订单分时段分布
            fillTodayOrdersData(response, request.getStoreId(), queryDate);

            // 2. 填充历史同期对比数据
            fillHistoricalComparisonData(response, request.getStoreId(), queryDate);

            // 3. 填充高峰期分析数据
            fillPeakAnalysisData(response, request.getStoreId(), queryDate);

            // 4. 填充门店对比数据
            fillStoreComparisonData(response, request.getStoreId(), queryDate);

            log.info("Successfully generated order analysis data for store: {}", request.getStoreId());

        } catch (Exception e) {
            log.error("Failed to get order analysis data for store: {}", request.getStoreId(), e);
            throw new RuntimeException("获取订单分析数据失败: " + e.getMessage(), e);
        }

        return response;
    }

    @Override
    public PosUsageResponse getPosUsageData(PosUsageRequest request) {
        log.info("Getting POS usage data for store: {}, date: {}", request.getStoreId(), request.getDate());

        PosUsageResponse response = new PosUsageResponse();

        // 设置默认日期为当天
        LocalDate queryDate = request.getDate() != null ? request.getDate() : LocalDate.now();

        try {
            // 1. 设置基础信息
            response.setStoreId(request.getStoreId());
            response.setStoreName("门店" + request.getStoreId()); // 可以从数据库查询实际门店名称
            response.setDate(queryDate.toString());

            // 2. 获取门店设备状态信息
            PosStoreStatusRequest statusRequest = new PosStoreStatusRequest();
            statusRequest.setStoreNo(Integer.parseInt(request.getStoreId()));
            PosStoreStatusResponse storeStatus = posStoreStatusService.getStoreStatus(statusRequest);

            // 3. 填充POS使用率数据
            fillPosUsageData(response, request.getStoreId(), queryDate, storeStatus);

            // 4. 填充各时段使用率数据
            fillHourlyUsageData(response, request.getStoreId(), queryDate, storeStatus);

            log.info("Successfully generated POS usage data for store: {}", request.getStoreId());

        } catch (Exception e) {
            log.error("Failed to get POS usage data for store: {}", request.getStoreId(), e);
            throw new RuntimeException("获取POS使用率数据失败: " + e.getMessage(), e);
        }

        return response;
    }

    /**
     * 填充能耗优化基础数据
     */
    private void fillEnergyOptimizationBasicData(EnergyOptimizationResponse response, PosStoreStatusResponse storeStatus) {
        int totalDevices = 0;
        int onlineDevices = 0;

        if (storeStatus != null && storeStatus.isSuccess() && storeStatus.getBody() != null) {
            var body = storeStatus.getBody();
            totalDevices = body.getStoreAllPosCount();
            onlineDevices = body.getAllOnlPosCount();
        }

        // 模拟计算能耗数据
        double dailyConsumption = onlineDevices * 1.2 + Math.random() * 50; // 基础能耗 + 随机波动
        response.setDailyConsumption(dailyConsumption);
        response.setDailyChange(-5.2 + Math.random() * 10 - 5); // -10% 到 +5% 的变化

        double monthlyConsumption = dailyConsumption * 30;
        response.setMonthlyConsumption(monthlyConsumption);
        response.setMonthlyChange(-8.3 + Math.random() * 15 - 7.5); // -15% 到 +7% 的变化

        double projectedConsumption = dailyConsumption * 0.86; // 优化后预计节约14%
        response.setProjectedConsumption(projectedConsumption);
        response.setSavingPercentage(14.3);

        response.setYearlySavingEnergy(dailyConsumption * 365 * 0.143);
        response.setYearlySavingCost(response.getYearlySavingEnergy() * 0.6); // 假设每度电0.6元
    }

    /**
     * 填充优化建议
     */
    private void fillOptimizationSuggestions(EnergyOptimizationResponse response, String storeId,
                                           LocalDate queryDate, PosStoreStatusResponse storeStatus) {
        List<EnergyOptimizationResponse.OptimizationSuggestion> suggestions = new ArrayList<>();

        // 建议1：非营业时段POS自动休眠
        EnergyOptimizationResponse.OptimizationSuggestion suggestion1 = new EnergyOptimizationResponse.OptimizationSuggestion();
        suggestion1.setTitle("非营业时段POS自动休眠");
        suggestion1.setType("软件调整");
        suggestion1.setDescription("通过软件升级，使POS机在非营业时段自动进入深度休眠状态，减少待机能耗。特别是在夜间21:00至次日7:00期间。");
        suggestion1.setPotentialSaving(120.0);
        suggestion1.setCostSaving(26280.0);
        suggestion1.setDifficulty(2);
        suggestion1.setRoi(1);
        suggestion1.setInvestment(5000.0);
        suggestions.add(suggestion1);

        // 建议2：替换高能耗旧设备
        EnergyOptimizationResponse.OptimizationSuggestion suggestion2 = new EnergyOptimizationResponse.OptimizationSuggestion();
        suggestion2.setTitle("替换高能耗旧设备");
        suggestion2.setType("硬件更新");
        suggestion2.setDescription("将运行超过5年的老旧POS设备更换为新一代节能设备，平均每台可节省40%的能耗。");
        suggestion2.setPotentialSaving(180.0);
        suggestion2.setCostSaving(39400.0);
        suggestion2.setDifficulty(4);
        suggestion2.setRoi(18);
        suggestion2.setInvestment(60000.0);
        suggestions.add(suggestion2);

        // 建议3：优化POS开机轮次
        EnergyOptimizationResponse.OptimizationSuggestion suggestion3 = new EnergyOptimizationResponse.OptimizationSuggestion();
        suggestion3.setTitle("优化POS开机轮次");
        suggestion3.setType("运营优化");
        suggestion3.setDescription("根据客流量预测，调整POS机开机数量和时间。在客流低谷期仅保持60%的设备运行，高峰期再开启全部设备。");
        suggestion3.setPotentialSaving(85.0);
        suggestion3.setCostSaving(18600.0);
        suggestion3.setDifficulty(3);
        suggestion3.setRoi(3);
        suggestion3.setInvestment(8000.0);
        suggestions.add(suggestion3);

        response.setOptimizationSuggestions(suggestions);
    }

    /**
     * 填充设备耗电量排行
     */
    private void fillDeviceConsumptionRanking(EnergyOptimizationResponse response, String storeId,
                                            LocalDate queryDate, PosStoreStatusResponse storeStatus) {
        List<EnergyOptimizationResponse.DeviceConsumptionRank> ranking = new ArrayList<>();

        if (storeStatus != null && storeStatus.isSuccess() && storeStatus.getBody() != null) {
            var body = storeStatus.getBody();
            List<Integer> onlinePosNos = body.getOnline();

            if (onlinePosNos != null && !onlinePosNos.isEmpty()) {
                int rank = 1;
                for (Integer posNo : onlinePosNos) {
                    if (rank > 10) break; // 只显示前10名

                    EnergyOptimizationResponse.DeviceConsumptionRank device = new EnergyOptimizationResponse.DeviceConsumptionRank();
                    device.setRank(rank);
                    device.setDeviceId(posNo.toString());
                    device.setType(posNo % 2 == 0 ? "manual" : "selfService"); // 简单分类
                    device.setLocation("收银区" + rank + "号");
                    device.setConsumption(6.8 - rank * 0.2 + Math.random() * 0.5); // 模拟耗电量
                    device.setUsageRate(60.0 - rank * 2 + Math.random() * 10); // 模拟使用率
                    device.setOptimizationStatus(rank <= 3 ? "not_optimized" : (rank <= 6 ? "pending" : "optimized"));

                    ranking.add(device);
                    rank++;
                }
            }
        }

        response.setDeviceConsumptionRanking(ranking);
    }

    /**
     * 填充今日订单分时段分布
     */
    private void fillTodayOrdersData(OrderAnalysisResponse response, String storeId, LocalDate queryDate) {
        try {
            LocalDateTime startTime = queryDate.atStartOfDay();
            LocalDateTime endTime = queryDate.atTime(LocalTime.MAX);

            // 查询当天的订单数据
            List<PosOrder> orders = posOrderRepository.findByStoreIdAndOrderTimeDateTimeBetween(
                    storeId, startTime, endTime);

            // 按小时分组统计订单
            Map<Integer, Map<String, Integer>> hourlyStats = new HashMap<>();

            // 初始化24小时数据
            for (int hour = 0; hour < 24; hour++) {
                Map<String, Integer> stats = new HashMap<>();
                stats.put("total", 0);
                stats.put("manual", 0);
                stats.put("selfService", 0);
                stats.put("canceled", 0);
                hourlyStats.put(hour, stats);
            }

            // 统计订单数据
            if (orders != null) {
                for (PosOrder order : orders) {
                    if (order.getOrderTimeDateTime() != null && order.getOutOrderId() != null) {
                        int hour = order.getOrderTimeDateTime().getHour();
                        String deviceType = getDeviceTypeFromOrderId(order.getOutOrderId());

                        Map<String, Integer> stats = hourlyStats.get(hour);
                        if (stats != null) {
                            stats.put("total", stats.get("total") + 1);
                            stats.put(deviceType, stats.get(deviceType) + 1);

                            // 模拟取消订单（实际应该从订单状态判断）
                            if (Math.random() < 0.02) { // 2%的取消率
                                stats.put("canceled", stats.get("canceled") + 1);
                            }
                        }
                    }
                }
            }

            // 转换为响应格式
            List<OrderAnalysisResponse.HourlyOrderData> todayOrders = new ArrayList<>();
            for (int hour = 0; hour < 24; hour++) {
                OrderAnalysisResponse.HourlyOrderData data = new OrderAnalysisResponse.HourlyOrderData();
                data.setHour(String.format("%02d:00", hour));
                Map<String, Integer> stats = hourlyStats.get(hour);

                if (stats != null) {
                    data.setTotal(stats.get("total"));
                    data.setManual(stats.get("manual"));
                    data.setSelfService(stats.get("selfService"));
                    data.setCanceled(stats.get("canceled"));
                } else {
                    data.setTotal(0);
                    data.setManual(0);
                    data.setSelfService(0);
                    data.setCanceled(0);
                }

                todayOrders.add(data);
            }

            response.setTodayOrders(todayOrders);

        } catch (Exception e) {
            log.error("Error filling today orders data for store: {}, date: {}", storeId, queryDate, e);
            response.setTodayOrders(new ArrayList<>());
        }
    }

    /**
     * 填充历史同期对比数据
     */
    private void fillHistoricalComparisonData(OrderAnalysisResponse response, String storeId, LocalDate queryDate) {
        Map<String, List<OrderAnalysisResponse.HistoricalComparisonData>> compareData = new HashMap<>();

        // 工作日对比数据（模拟）
        List<OrderAnalysisResponse.HistoricalComparisonData> weekdayData = new ArrayList<>();
        for (int hour = 8; hour <= 21; hour++) {
            OrderAnalysisResponse.HistoricalComparisonData data = new OrderAnalysisResponse.HistoricalComparisonData();
            data.setHour(String.format("%02d:00", hour));
            data.setToday((int)(200 + hour * 20 + Math.random() * 100));
            data.setLastWeek((int)(data.getToday() * (0.9 + Math.random() * 0.2)));
            data.setAvg((int)((data.getToday() + data.getLastWeek()) / 2));
            weekdayData.add(data);
        }
        compareData.put("weekday", weekdayData);

        // 周末对比数据（模拟）
        List<OrderAnalysisResponse.HistoricalComparisonData> weekendData = new ArrayList<>();
        for (int hour = 8; hour <= 21; hour++) {
            OrderAnalysisResponse.HistoricalComparisonData data = new OrderAnalysisResponse.HistoricalComparisonData();
            data.setHour(String.format("%02d:00", hour));
            data.setToday((int)(230 + hour * 25 + Math.random() * 120));
            data.setLastWeek((int)(data.getToday() * (0.85 + Math.random() * 0.3)));
            data.setAvg((int)((data.getToday() + data.getLastWeek()) / 2));
            weekendData.add(data);
        }
        compareData.put("weekend", weekendData);

        response.setCompareWithHistory(compareData);
    }

    /**
     * 填充高峰期分析数据
     */
    private void fillPeakAnalysisData(OrderAnalysisResponse response, String storeId, LocalDate queryDate) {
        OrderAnalysisResponse.PeakAnalysis peakAnalysis = new OrderAnalysisResponse.PeakAnalysis();

        // 早高峰
        OrderAnalysisResponse.PeakPeriod morningPeak = new OrderAnalysisResponse.PeakPeriod();
        morningPeak.setStartTime("08:00");
        morningPeak.setEndTime("10:00");
        morningPeak.setAvgOrders(350);
        peakAnalysis.setMorningPeak(morningPeak);

        // 午高峰
        OrderAnalysisResponse.PeakPeriod lunchPeak = new OrderAnalysisResponse.PeakPeriod();
        lunchPeak.setStartTime("12:00");
        lunchPeak.setEndTime("13:00");
        lunchPeak.setAvgOrders(680);
        peakAnalysis.setLunchPeak(lunchPeak);

        // 晚高峰
        OrderAnalysisResponse.PeakPeriod eveningPeak = new OrderAnalysisResponse.PeakPeriod();
        eveningPeak.setStartTime("18:00");
        eveningPeak.setEndTime("20:00");
        eveningPeak.setAvgOrders(750);
        peakAnalysis.setEveningPeak(eveningPeak);

        response.setPeakAnalysis(peakAnalysis);
    }

    /**
     * 填充门店对比数据
     */
    private void fillStoreComparisonData(OrderAnalysisResponse response, String storeId, LocalDate queryDate) {
        List<OrderAnalysisResponse.StoreComparisonData> storeComparison = new ArrayList<>();

        // 模拟门店对比数据
        String[] storeNames = {"北京朝阳门店", "上海南京路店", "广州天河店", "深圳福田店", "成都春熙路店"};
        String[] storeIds = {"1001", "1002", "1003", "1004", "1005"};

        for (int i = 0; i < storeNames.length; i++) {
            OrderAnalysisResponse.StoreComparisonData data = new OrderAnalysisResponse.StoreComparisonData();
            data.setStoreId(storeIds[i]);
            data.setStoreName(storeNames[i]);

            int totalOrders = (int)(7000 + Math.random() * 1500);
            data.setTotalOrders(totalOrders);
            data.setManualOrders((int)(totalOrders * (0.55 + Math.random() * 0.15)));
            data.setSelfServiceOrders(totalOrders - data.getManualOrders());

            storeComparison.add(data);
        }

        response.setStoreComparison(storeComparison);
    }

    /**
     * 填充POS使用率数据
     */
    private void fillPosUsageData(PosUsageResponse response, String storeId, LocalDate queryDate,
                                 PosStoreStatusResponse storeStatus) {
        if (storeStatus != null && storeStatus.isSuccess() && storeStatus.getBody() != null) {
            var body = storeStatus.getBody();

            // 人工POS使用情况
            PosUsageResponse.PosTypeUsage manualUsage = new PosUsageResponse.PosTypeUsage();
            manualUsage.setTotalHours(body.getManualPosCount() * 12.0); // 假设每台运行12小时
            manualUsage.setActiveHours(manualUsage.getTotalHours() * 0.43); // 43%使用率
            manualUsage.setUsageRate(43.1);

            // 生成人工POS设备详情
            List<PosUsageResponse.DeviceUsageDetail> manualDevices = new ArrayList<>();
            for (int i = 1; i <= Math.min(body.getManualPosCount(), 6); i++) {
                PosUsageResponse.DeviceUsageDetail device = new PosUsageResponse.DeviceUsageDetail();
                device.setDeviceId("M" + String.format("%03d", i));
                device.setUsageRate(40.0 + Math.random() * 15);
                device.setActiveHours(device.getUsageRate() / 100 * 12);
                device.setIdleHours(12 - device.getActiveHours());
                manualDevices.add(device);
            }
            manualUsage.setDevices(manualDevices);
            response.setManual(manualUsage);

            // 自助POS使用情况
            PosUsageResponse.PosTypeUsage selfServiceUsage = new PosUsageResponse.PosTypeUsage();
            selfServiceUsage.setTotalHours(body.getSelfPosCount() * 12.0);
            selfServiceUsage.setActiveHours(selfServiceUsage.getTotalHours() * 0.35);
            selfServiceUsage.setUsageRate(35.0);

            // 生成自助POS设备详情
            List<PosUsageResponse.DeviceUsageDetail> selfServiceDevices = new ArrayList<>();
            for (int i = 1; i <= Math.min(body.getSelfPosCount(), 5); i++) {
                PosUsageResponse.DeviceUsageDetail device = new PosUsageResponse.DeviceUsageDetail();
                device.setDeviceId("S" + String.format("%03d", i));
                device.setUsageRate(30.0 + Math.random() * 15);
                device.setActiveHours(device.getUsageRate() / 100 * 12);
                device.setIdleHours(12 - device.getActiveHours());
                selfServiceDevices.add(device);
            }
            selfServiceUsage.setDevices(selfServiceDevices);
            response.setSelfService(selfServiceUsage);
        }
    }

    /**
     * 填充各时段使用率数据
     */
    private void fillHourlyUsageData(PosUsageResponse response, String storeId, LocalDate queryDate,
                                   PosStoreStatusResponse storeStatus) {
        List<PosUsageResponse.HourlyUsageData> hourlyUsage = new ArrayList<>();

        // 生成24小时使用率数据
        for (int hour = 8; hour <= 21; hour++) {
            PosUsageResponse.HourlyUsageData data = new PosUsageResponse.HourlyUsageData();
            data.setHour(String.format("%02d:00", hour));

            // 模拟使用率：高峰期使用率高，低谷期使用率低
            double baseManualRate = 20.0;
            double baseSelfServiceRate = 15.0;

            if (hour >= 11 && hour <= 13) { // 午高峰
                baseManualRate = 75.0;
                baseSelfServiceRate = 55.0;
            } else if (hour >= 18 && hour <= 20) { // 晚高峰
                baseManualRate = 80.0;
                baseSelfServiceRate = 60.0;
            } else if (hour >= 9 && hour <= 11) { // 上午
                baseManualRate = 45.0;
                baseSelfServiceRate = 30.0;
            } else if (hour >= 16 && hour <= 18) { // 下午
                baseManualRate = 55.0;
                baseSelfServiceRate = 35.0;
            }

            data.setManual(baseManualRate + Math.random() * 10 - 5);
            data.setSelfService(baseSelfServiceRate + Math.random() * 10 - 5);

            hourlyUsage.add(data);
        }

        response.setHourlyUsage(hourlyUsage);
    }
}
