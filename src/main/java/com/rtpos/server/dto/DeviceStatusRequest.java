package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.rtpos.server.config.LocalDateDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.time.LocalDate;

/**
 * 设备状态查询请求DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "设备状态查询请求")
public class DeviceStatusRequest {

    @Schema(description = "门店ID", example = "1001", required = true)
    @JsonProperty("storeId")
    @NotBlank(message = "门店ID不能为空")
    private String storeId;

    @Schema(description = "查询日期，默认为当天", example = "2024-06-27")
    @JsonProperty("date")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    private LocalDate date;

    @Schema(description = "时间范围类型：today-今日，week-本周，month-本月", example = "today")
    @JsonProperty("timeRange")
    private String timeRange = "today";
}
