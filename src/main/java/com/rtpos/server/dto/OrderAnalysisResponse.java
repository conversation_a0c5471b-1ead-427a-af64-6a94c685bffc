package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 订单分析数据响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "订单分析数据响应")
public class OrderAnalysisResponse {

    @Schema(description = "今日订单分时段分布")
    @JsonProperty("todayOrders")
    private List<HourlyOrderData> todayOrders;

    @Schema(description = "历史同期对比")
    @JsonProperty("compareWithHistory")
    private Map<String, List<HistoricalComparisonData>> compareWithHistory;

    @Schema(description = "高峰期分析")
    @JsonProperty("peakAnalysis")
    private PeakAnalysis peakAnalysis;

    @Schema(description = "门店订单量对比")
    @JsonProperty("storeComparison")
    private List<StoreComparisonData> storeComparison;

    /**
     * 小时订单数据
     */
    @Data
    @Schema(description = "小时订单数据")
    public static class HourlyOrderData {
        @Schema(description = "小时", example = "08:00")
        @JsonProperty("hour")
        private String hour;

        @Schema(description = "总订单数")
        @JsonProperty("total")
        private Integer total;

        @Schema(description = "人工POS订单数")
        @JsonProperty("manual")
        private Integer manual;

        @Schema(description = "自助POS订单数")
        @JsonProperty("selfService")
        private Integer selfService;

        @Schema(description = "取消订单数")
        @JsonProperty("canceled")
        private Integer canceled;
    }

    /**
     * 历史对比数据
     */
    @Data
    @Schema(description = "历史对比数据")
    public static class HistoricalComparisonData {
        @Schema(description = "小时", example = "08:00")
        @JsonProperty("hour")
        private String hour;

        @Schema(description = "今日订单数")
        @JsonProperty("today")
        private Integer today;

        @Schema(description = "上周同期订单数")
        @JsonProperty("lastWeek")
        private Integer lastWeek;

        @Schema(description = "平均订单数")
        @JsonProperty("avg")
        private Integer avg;
    }

    /**
     * 高峰期分析
     */
    @Data
    @Schema(description = "高峰期分析")
    public static class PeakAnalysis {
        @Schema(description = "早高峰")
        @JsonProperty("morningPeak")
        private PeakPeriod morningPeak;

        @Schema(description = "午高峰")
        @JsonProperty("lunchPeak")
        private PeakPeriod lunchPeak;

        @Schema(description = "晚高峰")
        @JsonProperty("eveningPeak")
        private PeakPeriod eveningPeak;
    }

    /**
     * 高峰时段
     */
    @Data
    @Schema(description = "高峰时段")
    public static class PeakPeriod {
        @Schema(description = "开始时间")
        @JsonProperty("startTime")
        private String startTime;

        @Schema(description = "结束时间")
        @JsonProperty("endTime")
        private String endTime;

        @Schema(description = "平均订单数")
        @JsonProperty("avgOrders")
        private Integer avgOrders;
    }

    /**
     * 门店对比数据
     */
    @Data
    @Schema(description = "门店对比数据")
    public static class StoreComparisonData {
        @Schema(description = "门店ID")
        @JsonProperty("storeId")
        private String storeId;

        @Schema(description = "门店名称")
        @JsonProperty("storeName")
        private String storeName;

        @Schema(description = "总订单数")
        @JsonProperty("totalOrders")
        private Integer totalOrders;

        @Schema(description = "人工POS订单数")
        @JsonProperty("manualOrders")
        private Integer manualOrders;

        @Schema(description = "自助POS订单数")
        @JsonProperty("selfServiceOrders")
        private Integer selfServiceOrders;
    }
}
