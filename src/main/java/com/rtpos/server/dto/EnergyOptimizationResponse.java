package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 能耗优化数据响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "能耗优化数据响应")
public class EnergyOptimizationResponse {

    @Schema(description = "今日总能耗(kWh)")
    @JsonProperty("dailyConsumption")
    private Double dailyConsumption;

    @Schema(description = "今日能耗变化率(%)")
    @JsonProperty("dailyChange")
    private Double dailyChange;

    @Schema(description = "本月总能耗(kWh)")
    @JsonProperty("monthlyConsumption")
    private Double monthlyConsumption;

    @Schema(description = "本月能耗变化率(%)")
    @JsonProperty("monthlyChange")
    private Double monthlyChange;

    @Schema(description = "优化后预计能耗(kWh)")
    @JsonProperty("projectedConsumption")
    private Double projectedConsumption;

    @Schema(description = "节约百分比(%)")
    @JsonProperty("savingPercentage")
    private Double savingPercentage;

    @Schema(description = "年度节约能耗(kWh)")
    @JsonProperty("yearlySavingEnergy")
    private Double yearlySavingEnergy;

    @Schema(description = "年度节约成本(元)")
    @JsonProperty("yearlySavingCost")
    private Double yearlySavingCost;

    @Schema(description = "优化建议列表")
    @JsonProperty("optimizationSuggestions")
    private List<OptimizationSuggestion> optimizationSuggestions;

    @Schema(description = "设备耗电量排行")
    @JsonProperty("deviceConsumptionRanking")
    private List<DeviceConsumptionRank> deviceConsumptionRanking;

    /**
     * 优化建议
     */
    @Data
    @Schema(description = "优化建议")
    public static class OptimizationSuggestion {
        @Schema(description = "建议标题")
        @JsonProperty("title")
        private String title;

        @Schema(description = "建议类型")
        @JsonProperty("type")
        private String type;

        @Schema(description = "详细描述")
        @JsonProperty("description")
        private String description;

        @Schema(description = "潜在节约(kWh/天)")
        @JsonProperty("potentialSaving")
        private Double potentialSaving;

        @Schema(description = "每年节约成本(元)")
        @JsonProperty("costSaving")
        private Double costSaving;

        @Schema(description = "实施难度(1-5)")
        @JsonProperty("difficulty")
        private Integer difficulty;

        @Schema(description = "投资回报周期(月)")
        @JsonProperty("roi")
        private Integer roi;

        @Schema(description = "预计投资(元)")
        @JsonProperty("investment")
        private Double investment;
    }

    /**
     * 设备耗电量排行
     */
    @Data
    @Schema(description = "设备耗电量排行")
    public static class DeviceConsumptionRank {
        @Schema(description = "排名")
        @JsonProperty("rank")
        private Integer rank;

        @Schema(description = "设备ID")
        @JsonProperty("deviceId")
        private String deviceId;

        @Schema(description = "设备类型")
        @JsonProperty("type")
        private String type;

        @Schema(description = "设备位置")
        @JsonProperty("location")
        private String location;

        @Schema(description = "耗电量(kWh)")
        @JsonProperty("consumption")
        private Double consumption;

        @Schema(description = "使用率(%)")
        @JsonProperty("usageRate")
        private Double usageRate;

        @Schema(description = "优化状态")
        @JsonProperty("optimizationStatus")
        private String optimizationStatus;
    }
}
