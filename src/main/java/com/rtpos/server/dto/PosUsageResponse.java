package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * POS使用率数据响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "POS使用率数据响应")
public class PosUsageResponse {

    @Schema(description = "门店ID")
    @JsonProperty("storeId")
    private String storeId;

    @Schema(description = "门店名称")
    @JsonProperty("storeName")
    private String storeName;

    @Schema(description = "查询日期")
    @JsonProperty("date")
    private String date;

    @Schema(description = "人工POS使用情况")
    @JsonProperty("manual")
    private PosTypeUsage manual;

    @Schema(description = "自助POS使用情况")
    @JsonProperty("selfService")
    private PosTypeUsage selfService;

    @Schema(description = "各时段使用率")
    @JsonProperty("hourlyUsage")
    private List<HourlyUsageData> hourlyUsage;

    /**
     * POS类型使用情况
     */
    @Data
    @Schema(description = "POS类型使用情况")
    public static class PosTypeUsage {
        @Schema(description = "总运行时长(小时)")
        @JsonProperty("totalHours")
        private Double totalHours;

        @Schema(description = "有效使用时长(小时)")
        @JsonProperty("activeHours")
        private Double activeHours;

        @Schema(description = "使用率(%)")
        @JsonProperty("usageRate")
        private Double usageRate;

        @Schema(description = "设备详情列表")
        @JsonProperty("devices")
        private List<DeviceUsageDetail> devices;
    }

    /**
     * 设备使用详情
     */
    @Data
    @Schema(description = "设备使用详情")
    public static class DeviceUsageDetail {
        @Schema(description = "设备ID")
        @JsonProperty("deviceId")
        private String deviceId;

        @Schema(description = "使用率(%)")
        @JsonProperty("usageRate")
        private Double usageRate;

        @Schema(description = "有效使用时长(小时)")
        @JsonProperty("activeHours")
        private Double activeHours;

        @Schema(description = "闲置时长(小时)")
        @JsonProperty("idleHours")
        private Double idleHours;
    }

    /**
     * 小时使用率数据
     */
    @Data
    @Schema(description = "小时使用率数据")
    public static class HourlyUsageData {
        @Schema(description = "小时", example = "08:00")
        @JsonProperty("hour")
        private String hour;

        @Schema(description = "人工POS使用率(%)")
        @JsonProperty("manual")
        private Double manual;

        @Schema(description = "自助POS使用率(%)")
        @JsonProperty("selfService")
        private Double selfService;
    }
}
