package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 设备状态响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "设备状态响应")
public class DeviceStatusResponse {

    @Schema(description = "总设备数")
    @JsonProperty("totalDevices")
    private Integer totalDevices;

    @Schema(description = "在线设备数")
    @JsonProperty("onlineDevices")
    private Integer onlineDevices;

    @Schema(description = "活跃设备数")
    @JsonProperty("activeDevices")
    private Integer activeDevices;

    @Schema(description = "异常设备数")
    @JsonProperty("abnormalDevices")
    private Integer abnormalDevices;

    @Schema(description = "人工POS数量")
    @JsonProperty("manualCount")
    private Integer manualCount;

    @Schema(description = "自助POS数量")
    @JsonProperty("selfServiceCount")
    private Integer selfServiceCount;

    @Schema(description = "设备详情列表")
    @JsonProperty("devices")
    private List<DeviceDetail> devices;

    /**
     * 设备详情
     */
    @Data
    @Schema(description = "设备详情")
    public static class DeviceDetail {
        @Schema(description = "设备ID")
        @JsonProperty("deviceId")
        private String deviceId;

        @Schema(description = "设备类型：manual-人工POS，selfService-自助POS")
        @JsonProperty("type")
        private String type;

        @Schema(description = "设备位置")
        @JsonProperty("location")
        private String location;

        @Schema(description = "设备状态：active-活跃，idle-空闲，offline-离线，abnormal-异常")
        @JsonProperty("status")
        private String status;

        @Schema(description = "最后活跃时间")
        @JsonProperty("lastActiveTime")
        private String lastActiveTime;

        @Schema(description = "运行时长")
        @JsonProperty("runningTime")
        private String runningTime;
    }
}
