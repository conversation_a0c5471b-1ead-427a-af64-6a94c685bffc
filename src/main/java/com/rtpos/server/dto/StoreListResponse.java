package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 门店列表响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "门店列表响应")
public class StoreListResponse {

    @Schema(description = "门店列表")
    @JsonProperty("stores")
    private List<StoreInfo> stores;

    /**
     * 门店信息
     */
    @Data
    @Schema(description = "门店信息")
    public static class StoreInfo {
        @Schema(description = "门店ID")
        @JsonProperty("id")
        private String id;

        @Schema(description = "门店名称")
        @JsonProperty("name")
        private String name;

        @Schema(description = "区域序号")
        @JsonProperty("pgSeq")
        private String pgSeq;

        @Schema(description = "区域名称")
        @JsonProperty("pgName")
        private String pgName;

        @Schema(description = "子区域ID")
        @JsonProperty("subId")
        private String subId;

        @Schema(description = "子区域名称")
        @JsonProperty("subArea")
        private String subArea;
    }
}
