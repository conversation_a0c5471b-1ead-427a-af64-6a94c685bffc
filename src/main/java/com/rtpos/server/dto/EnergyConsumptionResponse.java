package com.rtpos.server.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 能耗数据响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "能耗数据响应")
public class EnergyConsumptionResponse {

    @Schema(description = "日能耗(kWh)")
    @JsonProperty("dailyConsumption")
    private Double dailyConsumption;

    @Schema(description = "月能耗(kWh)")
    @JsonProperty("monthlyConsumption")
    private Double monthlyConsumption;

    @Schema(description = "年能耗(kWh)")
    @JsonProperty("yearlyConsumption")
    private Double yearlyConsumption;

    @Schema(description = "设备能耗统计")
    @JsonProperty("devices")
    private DeviceConsumptionStats devices;

    @Schema(description = "各小时段能耗")
    @JsonProperty("hourlyConsumption")
    private List<HourlyConsumptionData> hourlyConsumption;

    @Schema(description = "历史对比数据")
    @JsonProperty("historicalComparison")
    private HistoricalComparisonStats historicalComparison;

    @Schema(description = "各设备类型能耗占比")
    @JsonProperty("consumptionByType")
    private List<ConsumptionByTypeData> consumptionByType;

    @Schema(description = "节能效果分析")
    @JsonProperty("savingAnalysis")
    private SavingAnalysisData savingAnalysis;

    @Schema(description = "设备详细能耗数据")
    @JsonProperty("deviceDetails")
    private List<DeviceEnergyDetail> deviceDetails;

    /**
     * 设备能耗统计
     */
    @Data
    @Schema(description = "设备能耗统计")
    public static class DeviceConsumptionStats {
        @Schema(description = "人工POS统计")
        @JsonProperty("manual")
        private DeviceTypeStats manual;

        @Schema(description = "自助POS统计")
        @JsonProperty("selfService")
        private DeviceTypeStats selfService;
    }

    /**
     * 设备类型统计
     */
    @Data
    @Schema(description = "设备类型统计")
    public static class DeviceTypeStats {
        @Schema(description = "设备数量")
        @JsonProperty("count")
        private Integer count;

        @Schema(description = "总能耗(kWh)")
        @JsonProperty("totalConsumption")
        private Double totalConsumption;

        @Schema(description = "平均每台能耗(kWh)")
        @JsonProperty("avgPerDevice")
        private Double avgPerDevice;
    }

    /**
     * 小时能耗数据
     */
    @Data
    @Schema(description = "小时能耗数据")
    public static class HourlyConsumptionData {
        @Schema(description = "小时", example = "08:00")
        @JsonProperty("hour")
        private String hour;

        @Schema(description = "人工POS能耗(kWh)")
        @JsonProperty("manual")
        private Double manual;

        @Schema(description = "自助POS能耗(kWh)")
        @JsonProperty("selfService")
        private Double selfService;
    }

    /**
     * 历史对比统计
     */
    @Data
    @Schema(description = "历史对比统计")
    public static class HistoricalComparisonStats {
        @Schema(description = "日对比数据")
        @JsonProperty("daily")
        private List<HistoricalData> daily;

        @Schema(description = "月对比数据")
        @JsonProperty("monthly")
        private List<HistoricalData> monthly;
    }

    /**
     * 历史数据
     */
    @Data
    @Schema(description = "历史数据")
    public static class HistoricalData {
        @Schema(description = "日期/月份")
        @JsonProperty("date")
        private String date;

        @Schema(description = "能耗(kWh)")
        @JsonProperty("consumption")
        private Double consumption;
    }

    /**
     * 按类型能耗数据
     */
    @Data
    @Schema(description = "按类型能耗数据")
    public static class ConsumptionByTypeData {
        @Schema(description = "设备类型")
        @JsonProperty("type")
        private String type;

        @Schema(description = "占比(%)")
        @JsonProperty("percentage")
        private Double percentage;
    }

    /**
     * 节能分析数据
     */
    @Data
    @Schema(description = "节能分析数据")
    public static class SavingAnalysisData {
        @Schema(description = "优化前能耗(kWh)")
        @JsonProperty("beforeOptimization")
        private Double beforeOptimization;

        @Schema(description = "优化后能耗(kWh)")
        @JsonProperty("afterOptimization")
        private Double afterOptimization;

        @Schema(description = "节约百分比(%)")
        @JsonProperty("savingPercentage")
        private Double savingPercentage;

        @Schema(description = "月节约量(kWh)")
        @JsonProperty("monthlySaving")
        private Double monthlySaving;

        @Schema(description = "年节约量(kWh)")
        @JsonProperty("yearlySaving")
        private Double yearlySaving;

        @Schema(description = "节约成本(元)")
        @JsonProperty("costSaving")
        private Double costSaving;
    }

    /**
     * 设备能耗详情
     */
    @Data
    @Schema(description = "设备能耗详情")
    public static class DeviceEnergyDetail {
        @Schema(description = "设备ID")
        @JsonProperty("deviceId")
        private String deviceId;

        @Schema(description = "设备类型")
        @JsonProperty("deviceType")
        private String deviceType;

        @Schema(description = "总能耗(kWh)")
        @JsonProperty("totalConsumption")
        private Double totalConsumption;

        @Schema(description = "节能率(%)")
        @JsonProperty("energySavingRate")
        private Double energySavingRate;

        @Schema(description = "节约金额(元)")
        @JsonProperty("savingAmount")
        private Double savingAmount;

        @Schema(description = "状态：normal-正常，abnormal-异常")
        @JsonProperty("status")
        private String status;
    }
}
