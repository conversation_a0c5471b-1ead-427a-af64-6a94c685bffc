#!/bin/bash

# 测试新增的Dashboard API接口
# 确保后端服务已启动在 http://localhost:8081

BASE_URL="http://localhost:8081/api/v1"

echo "=== 测试新增的Dashboard API接口 ==="
echo

# 测试能耗优化接口
echo "1. 测试能耗优化接口"
echo "POST ${BASE_URL}/pos/dashboard/energy-optimization"
curl -X POST "${BASE_URL}/pos/dashboard/energy-optimization" \
  -H "Content-Type: application/json" \
  -d '{
    "storeId": "1001",
    "date": "2024-06-27",
    "timeRange": "day"
  }' | jq '.'

echo
echo "----------------------------------------"
echo

# 测试订单分析接口
echo "2. 测试订单分析接口"
echo "POST ${BASE_URL}/pos/dashboard/order-analysis"
curl -X POST "${BASE_URL}/pos/dashboard/order-analysis" \
  -H "Content-Type: application/json" \
  -d '{
    "storeId": "1001",
    "date": "2024-06-27",
    "timeRange": "today"
  }' | jq '.'

echo
echo "----------------------------------------"
echo

# 测试POS使用率接口
echo "3. 测试POS使用率接口"
echo "POST ${BASE_URL}/pos/dashboard/pos-usage"
curl -X POST "${BASE_URL}/pos/dashboard/pos-usage" \
  -H "Content-Type: application/json" \
  -d '{
    "storeId": "1001",
    "date": "2024-06-27",
    "timeRange": "today"
  }' | jq '.'

echo
echo "----------------------------------------"
echo

# 测试原有的Dashboard接口（确保没有破坏）
echo "4. 测试原有Dashboard接口"
echo "POST ${BASE_URL}/pos/dashboard/data"
curl -X POST "${BASE_URL}/pos/dashboard/data" \
  -H "Content-Type: application/json" \
  -d '{
    "storeId": "1001",
    "date": "2024-06-27",
    "timeRange": "today"
  }' | jq '.'

echo
echo "=== 测试完成 ==="
