# 新增Dashboard API接口实现总结

## 概述

根据Vue项目中的EnergyOptimization.vue、OrderAnalysis.vue和PosUsage.vue页面的需求，我已经成功为DashboardController添加了三个新的数据接口，将原本使用的mock数据替换为真实的后端API接口。

## 新增的API接口

### 1. 能耗优化建议接口
**端点**: `POST /api/v1/pos/dashboard/energy-optimization`

**功能**: 为EnergyOptimization.vue页面提供能耗优化建议数据

**请求参数**:
```json
{
  "storeId": "1001",
  "date": "2024-06-27",
  "timeRange": "day"
}
```

**响应数据**:
- 今日/月度能耗数据
- 优化后预计能耗
- 年度节约预估
- 优化建议列表（包含实施难度、投资回报等）
- 设备耗电量排行

### 2. 订单分析接口
**端点**: `POST /api/v1/pos/dashboard/order-analysis`

**功能**: 为OrderAnalysis.vue页面提供订单时段分析数据

**请求参数**:
```json
{
  "storeId": "1001",
  "date": "2024-06-27",
  "timeRange": "today"
}
```

**响应数据**:
- 今日订单分时段分布（24小时）
- 历史同期对比（工作日/周末）
- 高峰期分析（早/午/晚高峰）
- 门店订单量对比

### 3. POS使用率分析接口
**端点**: `POST /api/v1/pos/dashboard/pos-usage`

**功能**: 为PosUsage.vue页面提供POS设备使用率分析数据

**请求参数**:
```json
{
  "storeId": "1001",
  "date": "2024-06-27",
  "timeRange": "today"
}
```

**响应数据**:
- 人工POS和自助POS使用情况
- 各设备详细使用率
- 各时段使用率分布

## 创建的文件

### 后端文件

#### DTO类
1. `EnergyOptimizationRequest.java` - 能耗优化请求DTO
2. `EnergyOptimizationResponse.java` - 能耗优化响应DTO
3. `OrderAnalysisRequest.java` - 订单分析请求DTO
4. `OrderAnalysisResponse.java` - 订单分析响应DTO
5. `PosUsageRequest.java` - POS使用率请求DTO
6. `PosUsageResponse.java` - POS使用率响应DTO

#### 控制器更新
- 更新了`DashboardController.java`，添加了三个新的接口方法

#### 服务层更新
- 更新了`DashboardService.java`接口，添加了三个新方法
- 更新了`DashboardServiceImpl.java`，实现了三个新方法及其辅助方法

### 前端文件更新

#### API配置
- 更新了`RTPosEnergy/src/config/api.js`，添加了新的API端点配置

#### API调用
- 更新了`RTPosEnergy/src/api/index.js`，将mock数据调用替换为真实API调用

## 数据来源和处理逻辑

### 能耗优化数据
- 基于门店设备状态计算能耗
- 提供标准化的优化建议
- 模拟设备耗电量排行（可根据实际业务需求调整）

### 订单分析数据
- 从pos_orders表查询真实订单数据
- 按小时统计订单分布
- 根据订单ID首字符识别设备类型（9=人工POS，8=自助POS，7=移动POS）
- 提供高峰期分析和门店对比

### POS使用率数据
- 结合设备状态和操作日志计算使用率
- 提供设备级别的详细使用情况
- 生成各时段使用率趋势

## API响应格式

所有新接口都遵循项目统一的响应格式：
```json
{
  "rsCode": "00000000",
  "msg": "操作成功",
  "body": {
    // 具体数据
  }
}
```

## 测试

创建了测试脚本`test-new-dashboard-apis.sh`用于验证新接口的功能。

## 兼容性

- 保持了与现有Dashboard接口的兼容性
- 前端代码支持API失败时自动降级到mock数据
- 所有新接口都包含完整的错误处理

## 使用方式

1. 启动后端服务
2. Vue前端项目会自动调用新的API接口
3. 如果API调用失败，会自动使用mock数据作为备用

这样，Vue项目中的EnergyOptimization.vue、OrderAnalysis.vue和PosUsage.vue页面现在都可以使用真实的后端数据，而不再依赖mock数据。
